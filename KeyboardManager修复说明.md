# KeyboardManager键盘遮挡问题修复说明

## 问题描述

在支付页面（CombinedPaymentController）中，当用户点击余额输入框时，键盘弹出会遮挡输入框，导致用户无法看到正在输入的内容。

从日志分析发现的问题：
- 输入框底部位置：657.67
- 键盘顶部位置：625.0  
- 需要向上移动：47.67像素
- 但调整后输入框底部变成：672.67（反而更低了）

## 根本原因分析

1. **容器识别问题**：KeyboardManager没有正确识别BasePresentController的布局结构
2. **调整计算错误**：ViewController调整时的移动距离计算有误
3. **布局时机问题**：BasePresentController需要更长的布局稳定时间
4. **边界检查不足**：没有充分考虑BasePresentController的特殊顶部结构

## 修复方案

### 1. 优化容器查找逻辑

```swift
/// 查找容器
private func findContainers(for textInputView: UIView) {
    // 重置容器状态，每次都重新查找以确保准确性
    // 增加搜索深度限制，避免无限循环
    // 改进ScrollView检测逻辑，确保找到真正可滚动的容器
}
```

**改进点：**
- 每次都重新查找容器，确保准确性
- 增加搜索深度限制（最大10层）
- 改进ScrollView检测逻辑
- 增加详细的调试日志

### 2. 增强ViewController调整逻辑

```swift
/// 调整ViewController
private func adjustViewController(_ viewController: UIViewController, overlap: CGFloat, duration: Double, curve: UInt) {
    // 强制布局更新，确保获取正确的frame
    // 区分BasePresentController和普通ViewController的处理逻辑
    // 改进移动距离计算和边界检查
}
```

**改进点：**
- 强制布局更新，确保frame准确
- 区分BasePresentController和普通ViewController
- 改进移动距离计算
- 增加边界安全检查
- 详细的调试日志输出

### 3. 专门的BasePresentController处理

```swift
/// 检查是否为BasePresentController
private func isBasePresentController(_ viewController: UIViewController) -> Bool

/// 为BasePresentController计算特殊的调整参数
private func calculateBasePresentControllerAdjustment(
    viewController: UIViewController,
    textInputFrame: CGRect,
    keyboardTop: CGFloat
) -> (shouldAdjust: Bool, moveDistance: CGFloat)
```

**特殊处理：**
- 识别BasePresentController类型
- 考虑标题栏高度（80px）
- 增加额外的键盘间距（10px）
- 确保标题栏始终可见

### 4. 优化键盘显示时机

```swift
/// 键盘将要显示
private func keyboardWillShow(_ notification: Notification) {
    // 对于BasePresentController，需要更长的延迟以确保布局稳定
    var delay: TimeInterval = isFirstKeyboardShow ? 0.1 : 0.0
    if let viewController = currentViewController,
       viewController.isKind(of: NSClassFromString("BasePresentController") ?? UIViewController.self) {
        delay = max(delay, 0.15) // BasePresentController需要更长的延迟
    }
}
```

**改进点：**
- BasePresentController使用更长的延迟（0.15秒）
- 确保布局完全稳定后再进行调整
- 增加详细的状态日志

### 5. 改进恢复逻辑

```swift
/// 恢复视图位置
private func restoreViews(duration: Double, curve: UInt) {
    // 增加容器有效性检查
    // 改进动画完成回调
    // 确保状态正确清理
}
```

**改进点：**
- 检查容器是否仍在视图层次中
- 改进动画完成状态处理
- 确保状态正确清理

## 修复效果

修复后的KeyboardManager能够：

1. **正确识别容器**：准确识别BasePresentController和其内部结构
2. **精确计算调整**：根据不同容器类型使用相应的调整算法
3. **安全边界控制**：确保调整不会导致内容移出屏幕
4. **稳定的恢复**：键盘隐藏时能正确恢复到原始状态
5. **详细的调试信息**：提供完整的调试日志便于问题排查

## 测试建议

1. **支付页面测试**：
   - 点击余额输入框
   - 验证键盘不会遮挡输入框
   - 验证输入内容可见
   - 验证键盘隐藏后视图正确恢复

2. **其他页面测试**：
   - 测试普通页面的输入框
   - 测试带ScrollView的页面
   - 测试其他BasePresentController页面

3. **边界情况测试**：
   - 快速切换输入框
   - 应用进入后台/前台
   - 横竖屏切换（如果支持）

## 注意事项

1. 修复主要针对BasePresentController，不会影响其他页面的键盘处理
2. 保持了原有的ScrollView优先处理逻辑
3. 增加了详细的日志输出，便于后续问题排查
4. 所有修改都向后兼容，不会破坏现有功能
